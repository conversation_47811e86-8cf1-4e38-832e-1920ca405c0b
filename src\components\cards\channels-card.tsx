import React from "react";

export default function ChanelsCard({ data, onClick }: any) {
  return (
    <div
      className="flex flex-col items-center justify-center cursor-pointer"
      onClick={onClick}
    >
      <div
        className="size-40 bg-blue-500 rounded-3xl bg-center bg-no-repeat bg-cover"
        style={{
          backgroundImage: `url(${data?.img_perfil_url})`,
        }}
      ></div>
      <div className="mt-[15px] flex flex-col justify-center">
        <p className="text-lg font-bold leading-[22px] pb-[5px]">
          {data?.nome}
        </p>
      </div>
    </div>
  );
}
