/** @type {import('tailwindcss').Config} */
module.exports = {
  important: true,
  darkMode: ["class"],
  content: ["./index.html", "./src/**/*.{js,jsx,ts,tsx}"],
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        primary: {
          DEFAULT: "#16181E",
          foreground: "#FFFFFF",
        },
        secondary: {
          DEFAULT: "#21242D",
          foreground: "#FFFFFF",
        },
        myblue: {
          0: "#19233A",
          100: "#151E33",
          200: "#2EA2F0",
          300: "#2DCEEF",
          400: "#27334F",
          500: "#19233A",
          600: "#444B5C",
          700: "#4BB1D7",
          900: "#20293D",
        },
        alert: "#BFB808",
        "alpha-grey": "#474F61",
        "alpha-blue": "#27334F",
        "active-menu": "#2EA2F0",
        "input-gray": "#AEB2BA",
        "back-arrow": "#AEB2BA88",
        "search-button": "#AEB2BA88",
        "metalic-silver": "#F2F2F2",
        "published-state": "#14C24F",
        "draft-state": "#C28714",
        border: "#FFFFFF70",
        input: "#FFFFFF80",
        ring: "#2EA2F0",
        background: "#151E33",
        foreground: "#FFFFFF",
        popover: {
          DEFAULT: "#FFFFFF",
          foreground: "#151E33",
        },
        card: {
          DEFAULT: "#16181E",
          foreground: "#FFFFFF",
        },
        destructive: {
          DEFAULT: "#BFB808",
          foreground: "#FFFFFF",
        },
        muted: {
          DEFAULT: "#16181E",
          foreground: "#FFFFFF70",
        },
        accent: {
          DEFAULT: "#16181E50",
          foreground: "#FFFFFF",
        },
        gradient: {
          start: "#2EA2F0",
          end: "#2DCEEF",
        },
      },
      backgroundImage: {
        "blue-gradient": "linear-gradient(to right, #2EA2F0, #2DCEEF)",
        "blue-gradient-diagonal":
          "linear-gradient(to bottom right, #2EA2F0, #2DCEEF)",
        "artist-gradient":
          "linear-gradient(135deg, #33688A 0%, #355E8B 47%, #20314F 100%)",
      },
      // borderRadius: {
      // 	lg: '8px',
      // 	md: '4px',
      // 	sm: '2px',
      // 	menu: '12px',
      // 	button: '32px',
      // 	banners: '16px',
      // 	tabs: '64px',
      // 	input: '999px',
      // 	card: '20px'
      // },
      keyframes: {
        "accordion-down": {
          from: {
            height: "0",
          },
          to: {
            height: "var(--radix-accordion-content-height)",
          },
        },
        "accordion-up": {
          from: {
            height: "var(--radix-accordion-content-height)",
          },
          to: {
            height: "0",
          },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
};
