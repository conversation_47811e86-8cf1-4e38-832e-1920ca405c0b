import React, { useState, useContext, useEffect, useRef } from "react";
// import {
//   div,
//   Button,
//   Stack,
//   Image,
//   FormControl,
//   Radio,
//   RadioGroup,
//   div,
//   InputRightAddon,
//   InputRightElement,
//   InputLeftElement,
//   Checkbox,
// } from "@chakra-ui/react";

import { AuthContext } from "../contexts/auth-context";
//components

import HeaderLogin from "../components/header/header-login";
//images

import Footer from "../components/header/footer";
import { useNavigate, useParams } from "react-router-dom";
import { FiEye, FiEyeOff } from "react-icons/fi";
import { theme } from "../styles/theme";
import { cn } from "@/Utils";
import { CustomInput } from "@/components/ui/custom-input";
import { Input } from "../components/ui/input";
import { Button } from "@/components/ui/button";
import { EmailAtIcon } from "@/components/icons/email-at-icon";
import { PasswordLockIcon } from "@/components/icons/password-lock-icon";
import { ArrowRightIcon } from "@/components/icons/arrow-right-icon";
import { toast } from "sonner";
import { useSignInMutation } from "@/data/user";
import { handleAuth } from "@/hooks/useAuthStatus";

export default function Login() {
  const classes = theme.styles.global;

  const navigate = useNavigate();
  const { redirect } = useParams();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [verPass, setVerPass] = useState(false);
  const [loading, setLoading] = useState(false);

  //const { signIn } = useContext(AuthContext);

  const loginButtonRef = useRef<HTMLButtonElement>(null);

  const { mutate: signIn, isLoading, isError, error } = useSignInMutation();

  const handleKeyDown = (event: any) => {
    //event.preventDefault();
    if (
      event.key === "Enter" &&
      !event.target.tagName.toLowerCase().match(/input|textarea/)
    ) {
      loginButtonRef.current?.click();
    }
  };

  useEffect(() => {
    // Add event listener when component mounts
    document.addEventListener("keydown", handleKeyDown);

    // Clean up by removing event listener when component unmounts
    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, []);

  useEffect(() => {
    if (isError) {
      toast.error("Wrong email or password", {
        description: "Try again or report it to admin.",
      });
    }
  }, [isError]);

  async function handleSubmit(event: React.SyntheticEvent) {
    event.preventDefault();

    setLoading(true);
    const data = { email: email, password: password };

    if (data.email !== "" && data.password !== "") {
      //await signIn(data, redirect);

      signIn(data, {
        onSuccess: (data) => {
          handleAuth(data, navigate, redirect);
        },
      });
    } else {
      toast.warning("Fill up every fields.", {
        description: "Enter your email and password",
      });
    }

    setLoading(false);
  }

  return (
    <div className="w-full h-screen flex flex-col justify-between items-center">
      <HeaderLogin />

      <form
        className="flex w-full max-w-[400px] bg-white p-8 rounded-xl flex-col shadow-gray-300 shadow-sm"
        onSubmit={handleSubmit}
      >
        <div className="space-y-4 text-black font-lato">
          <h1 className="text-center text-[30pt] align-center">Log In</h1>
          {/*<div justify="between" direction="row">
            <LoginOption logo={google} text="Google" onclick={() => {}} m="0" />
            <LoginOption logo={apple} text="Apple" onclick={() => {}} m="10px" />
            <LoginOption logo={facebook} text="Facebook" onclick={() => {}} m="10px" />
          </div>*/}

          <div className="justify-center text-center items-center w-full gap-0 flex">
            Don't have an account{" "}
            <span
              className="bg-transparent text-black font-bold hover:bg-transparent hover:underline cursor-pointer"
              onClick={(e) => {
                e?.preventDefault();
                navigate("/emailValidate");
              }}
            >
              <strong>Create one here</strong>
            </span>
          </div>
          <CustomInput.root className="rounded-input flex ">
            <CustomInput.left className=" flex items-center h-full opacity-80">
              <EmailAtIcon />
            </CustomInput.left>
            <Input
              name="email"
              type="email"
              placeholder="Email address"
              value={email}
              className="border-none focus:border-none focus:outline-none w-full text-black"
              onChange={(e) => setEmail(e.target.value)}
              // onKeyDown={handleKeyDown}
            />
          </CustomInput.root>
          <CustomInput.root className="rounded-input flex ">
            <CustomInput.left className=" flex items-center h-full opacity-80">
              <PasswordLockIcon className="text-black " />
            </CustomInput.left>
            <Input
              name="password"
              type={verPass ? "text" : "password"}
              placeholder="Password"
              className={`!border-none text-black `}
              value={password}
              onChange={(e) => setPassword(e.target.value)}
            />
            <CustomInput.right className="flex items-center h-full">
              {verPass ? (
                <FiEye cursor="pointer" onClick={() => setVerPass(!verPass)} />
              ) : (
                <FiEyeOff
                  cursor="pointer"
                  onClick={() => setVerPass(!verPass)}
                />
              )}
            </CustomInput.right>
          </CustomInput.root>

          <span
            style={{ fontSize: "8pt", color: "rgba(0,0,0,.5)" }}
            className="px-3"
          >
            Must have at least 8 characters.
          </span>
        </div>
        <div className="flex justify-between items-center text-black h-full mt-6">
          <Button
            type="submit"
            className="text-white bg-gradient-to-l from-[#2EA2F0] to-[#2DCEEF] flex gap-3 rounded-full font-bold"
            ref={loginButtonRef}
          >
            {<ArrowRightIcon />}
            Login
          </Button>
        </div>
        <div
          className="mt-5 w-full h-5 text-center bg-transparent cursor-pointer text-[rgba(0,0,0,.5)] text-[10pt] hover:bg-transparent hover:underline"
          onClick={() => {
            navigate("/emailValidateToReset");
          }}
        >
          Forgot my password
        </div>
      </form>
      {/*<Image src={payments} />*/}
      <Footer />
    </div>
    //</div>
  );
}
