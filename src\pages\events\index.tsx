import React, { useEffect, useState } from "react";
import { Loader2 } from "lucide-react";
import Header from "@/components/header/header";
import Sidebar from "@/components/sidebar/sidebar";

import serviceBackground from "@/images/serviceBackground.svg";
import serviceThumb from "../../images/serviceThumb.svg";
import { TabsContianer } from "./subcomponents/TabsContianer";
import { NewsCard } from "../../components/cards/news-card";
import { RightSideBar } from "@/components/right-sidebar";
import { api_tickets } from "@/services/api_tickets";
import { EventsCard } from "@/components/cards/events-card";
import { toast } from "sonner";
import { Label } from "@/components/ui/label";
import BaseLayout from "@/components/layout/BaseLayout";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { restClient } from "@/services/api-muska-full";

const Events = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [events, setEvents] = useState<any>([]);
  const [batch, setBatch] = useState<any>([]);
  const [tickets, setTickets] = useState<any>([]);
  const [active, setActive] = useState<any>(1);

  console.log({ events });

  const fetchPosts = async () => {
    try {
      setIsLoading(true);
      const responsePost: any = await restClient.get("/events");

      if (responsePost && responsePost?.status === 200) {
        setEvents(responsePost.data);
      }
    } catch (errorReadEvents) {
      console.log({ errorReadEvents });
      toast.error("Failed to retrieve posts.", {
        description: "Try again or report it to admin.",
        duration: 4000,
      });
    }
    setIsLoading(false);
  };

  useEffect(() => {
    fetchPosts();
  }, []);

  // Helper component for empty states
  const EmptyState = ({ message }: { message: string }) => (
    <div className="flex items-center justify-center my-10">
      <Label className="text-slate-500">{message}</Label>
    </div>
  );

  // Helper component for loading state
  const LoadingSpinner = () => (
    <div className="flex items-center justify-center w-full h-48">
      <Loader2 className="h-8 w-8 animate-spin" style={{ color: "#2EA2F0" }} />
    </div>
  );

  // Render event grid with filtering
  const renderEventGrid = (filteredEvents: any[], emptyMessage: string) => {
    if (isLoading) {
      return <LoadingSpinner />;
    }

    if (filteredEvents?.length > 0) {
      return (
        <div className="flex w-full">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 h-full w-3/4">
            {filteredEvents.map((item: any, index: number) => (
              <div key={index} className="w-full">
                <EventsCard data={item} />
              </div>
            ))}
          </div>
        </div>
      );
    }

    return <EmptyState message={emptyMessage} />;
  };

  return (
    <BaseLayout>
      <div className="flex flex-col w-full h-full gap-4">
        <div className="p-4 flex-1 mx-6 bg-myblue-100 h-full">
          {/* Hero Section */}
          <div
            className="
              rounded-md
              flex 
              justify-center 
              flex-col 
              bg-no-repeat 
              bg-cover 
              bg-center 
              h-full 
              w-full 
              mt-4 
              p-5 
              pt-20 
              pb-20
            "
            style={{ backgroundImage: `url(${serviceBackground})` }}
          >
            <div className="flex justify-between items-center">
              <div className="w-1/4">
                <Label className="text-base">
                  Find the Best <strong>Events</strong> for Music in Muska
                  World.
                </Label>
              </div>

              <div className="w-1/4 h-full flex items-center">
                <div className="py-4">[ Ad ]</div>
              </div>
            </div>
          </div>

          {/* Tabs Section */}
          <Tabs defaultValue="all" className="w-full">
            <TabsList className="w-full bg-transparent flex gap-5 text-lg font-bold mt-4">
              <TabsTrigger
                onClick={() => setActive(1)}
                value="all"
                className={`px-7 py-2 rounded-full transition-all duration-200 ${
                  active === 1
                    ? "bg-myblue-300 shadow-md shadow-myblue-300/50"
                    : "bg-myblue-500 hover:bg-myblue-400"
                }`}
              >
                All
              </TabsTrigger>
              <TabsTrigger
                onClick={() => setActive(2)}
                value="festivals"
                className={`px-7 py-2 rounded-full transition-all duration-200 ${
                  active === 2
                    ? "bg-myblue-300 shadow-md shadow-myblue-300/50"
                    : "bg-myblue-500 hover:bg-myblue-400"
                }`}
              >
                Festivals
              </TabsTrigger>
              <TabsTrigger
                onClick={() => setActive(3)}
                value="concerts"
                className={`px-7 py-2 rounded-full transition-all duration-200 ${
                  active === 3
                    ? "bg-myblue-300 shadow-md shadow-myblue-300/50"
                    : "bg-myblue-500 hover:bg-myblue-400"
                }`}
              >
                Concerts
              </TabsTrigger>
              <TabsTrigger
                onClick={() => setActive(4)}
                value="nightClub"
                className={`px-7 py-2 rounded-full transition-all duration-200 ${
                  active === 4
                    ? "bg-myblue-300 shadow-md shadow-myblue-300/50"
                    : "bg-myblue-500 hover:bg-myblue-400"
                }`}
              >
                NightClub
              </TabsTrigger>
              <TabsTrigger
                value="DJSet"
                onClick={() => setActive(5)}
                className={`px-7 py-2 rounded-full transition-all duration-200 ${
                  active === 5
                    ? "bg-myblue-300 shadow-md shadow-myblue-300/50"
                    : "bg-myblue-500 hover:bg-myblue-400"
                }`}
              >
                DJ Sets
              </TabsTrigger>
            </TabsList>

            {/* Tab Content */}
            <TabsContent value="all" className="mt-6">
              {renderEventGrid(
                events,
                "We couldn't find any event. Create yours by clicking in the Create button above.",
              )}
            </TabsContent>

            <TabsContent value="festivals" className="mt-6">
              {renderEventGrid(
                events.filter((event: any) => event.category === "Festival"),
                "We couldn't find any event in this category. Create yours by clicking in the Create button above.",
              )}
            </TabsContent>

            <TabsContent value="concerts" className="mt-6">
              {renderEventGrid(
                events.filter((event: any) => event.category === "Concert"),
                "We couldn't find any event in this category. Create yours by clicking in the Create button above.",
              )}
            </TabsContent>

            <TabsContent value="nightClub" className="mt-6">
              {renderEventGrid(
                events.filter((event: any) => event.category === "NightClub"),
                "We couldn't find any event in this category. Create yours by clicking in the Create button above.",
              )}
            </TabsContent>

            <TabsContent value="DJSet" className="mt-6">
              {renderEventGrid(
                events.filter((event: any) => event.category === "Dj sets"),
                "We couldn't find any event in this category. Create yours by clicking in the Create button above.",
              )}
            </TabsContent>
          </Tabs>

          <RightSideBar />
        </div>
      </div>
    </BaseLayout>
  );
};

export { Events };
