// hooks/useAuthStatus.js

import { API_ENDPOINTS } from "@/data/client/api-endpoints";
import { api_muskaLive } from "@/services/api_muskaLive";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { setCookie, parseCookies } from "nookies";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
// API call that checks if the user is logged in

export const useAuthStatus = () => {
  const queryClient = useQueryClient();

  //   const data = queryClient.getQueryData([API_ENDPOINTS.AUTH.SIGN_IN]);

  // console.log({ data });

  const { "muska.token": token } = parseCookies();

  if (token) {
    return {
      isAuthenticated: true,
    };
  }

  return {
    isAuthenticated: false,
  };
};

export const handleAuth = (data: any, navigate: any, redirect: any) => {
  const { token, name, image, type_user, artist, provider, id } = data;

  setCookie(undefined, "muska.token", token, {
    maxAge: 60 * 60 * 24 * 30, // 30 days
    path: "/",
  });

  muskaLiveSession(token, id);

  switch (redirect) {
    case "challenges":
      window.location.href = `https://challenge.muskaapp.com/login/${token}`;
      break;
    default:
      navigate("/report");
  }
};

async function muskaLiveSession(token: any, id: any) {
  try {
    const response: any = await api_muskaLive.post("/sessions", {
      token,
      id,
    });

    if (response.status === 200) {
      const newtoken = response.data.token;

      setCookie(undefined, "muska_live.token", newtoken, {
        maxAge: 60 * 60 * 24 * 30, // 30 days
        path: "/",
      });

      api_muskaLive.defaults.headers.common.Authorization = `Bearer ${newtoken}`;
    }
  } catch (e) {
    toast.error("Error", {
      description: "The connection with Muska Live was failed.",
    });
  }
}
