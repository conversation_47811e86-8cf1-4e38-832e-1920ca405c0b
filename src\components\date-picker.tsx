"use client";
import React from "react";
import { useState, useEffect } from "react";
import { format, parse, isValid } from "date-fns";
import { CalendarIcon } from "lucide-react";

import { cn } from "@/utils/utils";
import { Button } from "@/components/ui/button";

import { Input } from "@/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Label } from "@/components/ui/label";
import { Calendar } from "./ui/v2/calendar";

export default function DatePicker({ onChange, defaultValue, label }: any) {
  const [date, setDate] = useState<Date>();
  const [inputValue, setInputValue] = useState("");
  const [open, setOpen] = useState(false);
  const [isValidInput, setIsValidInput] = useState(true);

  // Update input when date changes from calendar
  useEffect(() => {
    if (date) {
      setInputValue(format(date, "MM/dd/yyyy"));
      onChange?.(format(date, "yyyy-MM-dd")); // Changed to ISO format
      setIsValidInput(true);
    }
  }, [date]);

  const handleInputChange = (value: string) => {
    setInputValue(value);

    if (!value) {
      setDate(undefined);
      setIsValidInput(true);
      onChange?.(undefined);
      return;
    }

    // Check if we have a 4-digit year in the input
    // Look for 4 consecutive digits that could be a year
    const yearPattern = /\b\d{4}\b/;
    const hasCompleteYear = yearPattern.test(value);

    if (!hasCompleteYear) {
      setIsValidInput(true); // Don't show error until we have a complete year
      return;
    }

    // Try to parse various date formats
    const formats = [
      "MM/dd/yyyy",
      "M/d/yyyy",
      "MM-dd-yyyy",
      "M-d-yyyy",
      "yyyy-MM-dd",
      "M/dd/yyyy",
      "MM/d/yyyy",
      "dd/MM/yyyy",
      "dd-MM-yyyy",
      "dd-M-yyyy",
      "d-MM-yyyy",
      "d-M-yyyy",
    ];
    let parsedDate: Date | undefined;

    for (const formatString of formats) {
      try {
        const parsed = parse(value, formatString, new Date());
        if (isValid(parsed)) {
          parsedDate = parsed;
          break;
        }
      } catch {
        // Continue to next format
      }
    }

    if (parsedDate && isValid(parsedDate)) {
      setDate(parsedDate);
      setIsValidInput(true);
      onChange?.(format(parsedDate, "yyyy-MM-dd")); // Changed to ISO format
    } else {
      // Only show invalid state if we have a complete year but can't parse
      setIsValidInput(false);
      setDate(undefined);
    }
  };

  const handleCalendarSelect = (newDate: Date | undefined) => {
    setDate(newDate);
    setOpen(false);
  };

  // Handle blur event to validate complete input
  const handleInputBlur = () => {
    if (!inputValue) return;

    const formats = [
      "MM/dd/yyyy",
      "M/d/yyyy",
      "MM-dd-yyyy",
      "M-d-yyyy",
      "yyyy-MM-dd",
      "M/dd/yyyy",
      "MM/d/yyyy",
    ];

    let parsedDate: Date | undefined;

    for (const formatString of formats) {
      try {
        const parsed = parse(inputValue, formatString, new Date());
        if (isValid(parsed)) {
          parsedDate = parsed;
          break;
        }
      } catch {
        // Continue to next format
      }
    }

    if (parsedDate && isValid(parsedDate)) {
      setDate(parsedDate);
      setIsValidInput(true);
      setInputValue(format(parsedDate, "MM/dd/yyyy")); // Keep display format as MM/dd/yyyy
      onChange?.(format(parsedDate, "yyyy-MM-dd")); // Changed to ISO format
    } else {
      setIsValidInput(false);
    }
  };

  return (
    <div className="flex flex-col items-start justify-center gap-2">
      <Label className="text-lg" htmlFor="date-input">
        {label || "Date"}
      </Label>
      <div className="space-y-2 w-full">
        <div className="flex gap-2 w-full">
          <div className="flex-1 w-full">
            <Input
              id="date-input"
              type="text"
              placeholder="MM/DD/YYYY"
              value={inputValue}
              onChange={(e) => handleInputChange(e.target.value)}
              onBlur={handleInputBlur}
              className={cn(
                "w-full",
                !isValidInput && "border-red-500 focus-visible:ring-red-500",
              )}
            />
            {!isValidInput && inputValue && (
              <p className="text-sm text-red-500 mt-1">
                Please enter a valid date (MM/DD/YYYY)
              </p>
            )}
          </div>

          <Popover open={open} onOpenChange={setOpen}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                size="icon"
                className="shrink-0 bg-transparent"
              >
                <CalendarIcon className="h-4 w-4" />
                <span className="sr-only">Open calendar</span>
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0 " align="end">
              <Calendar
                mode="single"
                selected={date}
                onSelect={handleCalendarSelect}
                initialFocus
                className="border border-amber-50 bg-background rounded-lg"
              />
            </PopoverContent>
          </Popover>
        </div>
      </div>

      {date && isValidInput && (
        <div className="p-3 bg-green-50 border border-green-200 rounded-md">
          <p className="text-sm text-green-800">
            <strong>Selected:</strong> {format(date, "EEEE, MMMM do, yyyy")}
          </p>
        </div>
      )}
    </div>
  );
}
