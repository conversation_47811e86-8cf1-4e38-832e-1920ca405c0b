import { useQuery } from "@tanstack/react-query";
import { TopTracksQueryOptions } from "./types";
import { adminClient } from "./client";

export const useTopTracksQuery = ({
  start,
  end,
  limit,
}: TopTracksQueryOptions) => {
  return useQuery({
    queryKey: ["topListenTracks", { start, end, limit }],
    queryFn: () => adminClient.fetchTopTracks({ start, end, limit }),
    enabled: <PERSON><PERSON><PERSON>(start && end && limit),
    // queryFn: () => artistClient.getArtist({ artistId }),
  });
};
