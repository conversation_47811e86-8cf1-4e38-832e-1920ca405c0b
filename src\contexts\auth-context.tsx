import React, { createContext, useEffect, useState } from "react";
import { parseCookies, setCookie, destroyCookie } from "nookies";
import { useNavigate, useParams } from "react-router-dom";
import { api } from "../services/api";
import { api_muskaLive } from "../services/api_muskaLive";

// import { UserType } from "../utils/types";
import { UserType } from "../utils/types";

import { toast } from "sonner";
import path from "path";
import { useQueryClient } from "@tanstack/react-query";
import { clearQueryCache } from "@/App";

export const AuthContext = createContext<any>({});
let loggedUserId: string | number = 0;
function parseJwt(token: any) {
  if (!token) {
    return;
  }
  const base64Url = token.split(".")[1];
  const base64 = base64Url.replace("-", "+").replace("_", "/");
  return JSON.parse(window.atob(base64));
}

export function AuthProvider({ children }: any) {
  const navigate = useNavigate();
  const [user, setUser] = useState<UserType | null>(null);
  const [useId, setUserId] = useState<any>("");
  const [userType, setUserType] = useState<any>("");
  const [isAuthenticated, setIsAutheticated] = useState<any>(false);

  const queryClient = useQueryClient();

  useEffect(() => {
    checkAuth();
  }, []);

  async function checkAuth() {
    if (typeof window !== undefined) {
      let page = window.location.pathname;
    }

    const { "muska.token": token } = parseCookies();

    if (token) {
      let users = parseJwt(token);

      api.defaults.headers.common.Authorization = `Bearer ${token}`;

      setUser(users);

      setIsAutheticated(true);
      if (users.type_user === 3) {
        loggedUserId = users.artist.id;
      } else {
        loggedUserId = users.id;
      }
      setUserId(loggedUserId?.toString());
      setUserType(users.type_user);

      /* window.localStorage.setItem("user", JSON.stringify(user)) */

      if (!users) {
        navigate("/");
      }
      // else {
      //   navigate("/report", { replace: true });
      // }
    } else {
      setIsAutheticated(false);
    }
  }

  async function signIn(data: any, redirect: string | undefined) {
    try {
      const { email, password } = data;
      let response;
      try {
        response = await api.post("user/login", { email, password });
      } catch (error) {
        toast.error("Wrong email or password", {
          description: "Try again or report it to admin.",
        });
        return;
      }

      const { token, name, image, type_user, artist, provider, id } =
        response.data;

      setCookie(undefined, "muska.token", token, {
        maxAge: 60 * 60 * 24 * 30, // 30 days
        path: "/",
      });

      setUser({
        artist,
        email,
        name,
        image,
        type_user,
        provider,
        id,
      });

      if (type_user === 4) {
        //loggedUserId = provider.id
        loggedUserId =
          user && user !== null && user?.id !== null ? user?.id : 0;
      } else if (type_user === 3) {
        loggedUserId = artist?.id;
      } else {
        loggedUserId = id;
      }

      muskaLiveSession(token, id);

      setUserId(loggedUserId);
      setUserType(type_user);

      if (type_user === null) {
        navigate("/validar");
      } else {
        setIsAutheticated(true);
        switch (redirect) {
          case "challenges":
            window.location.href = `http://localhost:3011/login/${token}`;
            break;
          default:
            navigate("/report");
        }
      }
    } catch (err) {
      console.log({ err });
      toast.error("Invalid data.", {
        description: "Please double check your email and password.",
      });
    }
  }

  function signOut() {
    queryClient.invalidateQueries();
    queryClient.clear();
    queryClient.resetQueries();
    queryClient.removeQueries();
    clearQueryCache();
    console.log("Signing out...");
    window.localStorage.removeItem("user");
    destroyCookie(undefined, "muska.token", { path: "/" });
    destroyCookie(undefined, "muska_live.token", { path: "/" });
    navigate("/");
  }

  async function register(props: any) {
    try {
      var data = {
        email: props.email,
        name: props.username,
        password: props.password,
        date: props.date,
        type_user: 4,
      };

      /* to create a provider linked to the account  */

      const responseProvider: any = await api
        .post("provider", { name: `${data.name}` })
        .catch((err) => {
          toast.error("Failed to create a provider to your account", {
            description: "Try again or report it to admin.",
          });
        });

      if (responseProvider.status === 200) {
        var newData = {
          email: props.email,
          name: props.username,
          password: props.password,
          date: props.date,
          type_user: 4,
          type_id: responseProvider.data.id,
        };

        const response: any = await api
          .post("user/register", newData)
          .catch((err) => {
            toast.error("Failed to register user", {
              description: "Try again or report it to admin.",
            });
          });

        if (response.status === 200) {
          const { token, name, image, type_user, artist, type_id, id } =
            response?.data;

          setUser({
            artist,
            email: newData.email,
            name,
            image,
            type_user,
            type_id,
            id,
            provider: responseProvider?.data,
          });

          setCookie(undefined, "muska.token", token, {
            maxAge: 60 * 60 * 24 * 30, // 30 days
            path: "/",
          });

          api.defaults.headers.common.Authorization = `Bearer ${token}`;

          navigate("/");
          toast.success("Conta criada com sucesso.", {
            description: "Proceda com o seu login",
          });
        } else if (response.status === 400) {
          toast.error("Dados Invalido.", {
            description: "Utilizador ja existe",
          });
        }
      }
    } catch (err) {
      toast.warning("Invalid data.", {
        description: "Please check your email and password.",
      });
    }
  }

  async function registerSocial(props: any) {
    try {
      var data = { email: "" };

      const email = ""; // to be checked

      const response: any = await api.post("user/socialRegister");

      const { token, name, image, type_user, artist, provider, id } =
        response.data;

      setUser({
        artist,
        email,
        name,
        image,
        type_user,
        provider,
        id,
      });

      setCookie(undefined, "muska.token", token, {
        maxAge: 60 * 60 * 24 * 30, // 30 days
        path: "/",
      });

      if (response.status === 200) {
        navigate("/");
      } else if (response.status === 400) {
        toast.warning("Invalid data.");
      }
    } catch (err) {
      toast.error("Invalid data.", {
        description: "Something went wrond with the social media.",
      });
    }
  }

  async function muskaLiveSession(token: any, id: any) {
    try {
      const response: any = await api_muskaLive.post("/sessions", {
        token,
        id,
      });

      if (response.status === 200) {
        const newtoken = response.data.token;

        setCookie(undefined, "muska_live.token", newtoken, {
          maxAge: 60 * 60 * 24 * 30, // 30 days
          path: "/",
        });

        api_muskaLive.defaults.headers.common.Authorization = `Bearer ${newtoken}`;
      }
    } catch (e) {
      toast.error("Error", {
        description: "The connection with Muska Live was failed.",
      });
    }
  }

  return (
    <AuthContext.Provider
      value={{
        signIn,
        registerSocial,
        signOut,
        setUser,
        isAuthenticated,
        user,
        useId,
        userType,
        register,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}
