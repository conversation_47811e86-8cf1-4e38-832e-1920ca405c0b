// import { usePathname } from "next/navigation";
import React from "react";
import { Link, useLocation } from "react-router-dom";
import { CalendarIcon } from "../icons/calendar-icon";
import { FileIcon } from "../icons/file-icon";
import { UsersIcon } from "../icons/user-icon";
import ColapsibleItem from "./sidebar-collapsible";
import { ArtistsIcon } from "../icons/artists-icon";
import { PressKitIcon } from "../icons/presskit-icon";
import { ChannelsIcon } from "../icons/channels-icon";
import AdminColapsibleItem from "./sidebar-admin-collapsible";
import { useMeQuery } from "@/data/user";

export default function Sidebar() {
  const { pathname } = useLocation();

  // console.log({ pathname });

  const { data: user, isLoading, error } = useMeQuery();

  return (
    <div className="flex h-screen max-h-screen flex-col bg-background-secondary  text-white ">
      <div className="flex-1 overflow-auto">
        <nav className="grid gap-2 px-4 py-4 text-md font-medium">
          <ColapsibleItem />

          <Link
            className={`flex items-center gap-3 rounded-menu px-3 py-3 transition-colors hover:text-white ${
              pathname.includes("/channels")
                ? "font-bold bg-alpha-blue text-active-menu"
                : "font-regular text-white"
            }`}
            to="/channels"
            //target="_blank"
          >
            <ChannelsIcon className="h-4 w-4" />
            <span>Channels</span>
          </Link>
          <Link
            className={`flex items-center gap-3 rounded-menu px-3 py-3 transition-colors hover:text-white ${
              pathname.includes("/artist")
                ? "font-bold bg-alpha-blue text-active-menu"
                : "font-regular"
            }`}
            to="/artist"
            // target="_blank"
          >
            <ArtistsIcon className="h-4 w-4 " />
            <span>Artists</span>
          </Link>
          <Link
            className={`flex items-center gap-3 px-3 py-3 transition-colors hover:text-white rounded-menu ${
              pathname.includes("/challenges")
                ? "font-bold bg-alpha-blue text-active-menu"
                : "font-regular"
            }`}
            to="https://challenge.muskaapp.com/challenges"
            target="_self"
          >
            <FileIcon className="h-4 w-4" />
            <span>Challenges</span>
          </Link>
          {/* <Link
            className={`flex items-center gap-3 px-3 py-3 transition-colors hover:text-white rounded-menu ${
              pathname.includes("/presskit")
                ? "font-bold bg-alpha-blue text-active-menu"
                : "font-regular"
            }`}
            to="/presskit"
          >
            <PressKitIcon className="h-4 w-4 " />
            <span>Preskit</span>
          </Link> */}
          {/* <Link
            className={`flex items-center gap-3 px-3 py-3 transition-colors hover:text-white rounded-menu ${
              pathname.includes("/events")
                ? "font-bold bg-alpha-blue text-active-menu"
                : "font-regular"
            }`}
            to="/events"
          >
            <PressKitIcon className="h-4 w-4 " />
            <span>Events</span>
          </Link> */}

          {user?.id === "55dbfce4-e8c0-47f3-878b-78003b544c86" && (
            <AdminColapsibleItem />
          )}
        </nav>
      </div>
    </div>
  );
}

// import { useEffect, useRef, useState } from "react";
// import { Box } from "@chakra-ui/react";

// import {
//   Link,
//   NavLink,
//   Navigate,
//   useLocation,
//   useNavigate,
// } from "react-router-dom";
// import generalIcon from "../../images/generalIcon.svg";
// import { MusicNoteIcon } from "../icons/music-note";
// import { cn } from "../../Utils";
// import SidebarLinkGroup from "./sidebar-link-group";

// export default function Sidebar({ sidebarOpen, setSidebarOpen }: any) {
//   const location = useLocation();
//   const { pathname } = location;

//   const trigger = useRef(null);
//   const sidebar = useRef(null);

//   const storedSidebarExpanded = localStorage.getItem("sidebar-expanded");
//   const [sidebarExpanded, setSidebarExpanded] = useState(
//     storedSidebarExpanded === null ? false : storedSidebarExpanded === "true",
//   );

//   // close on click outside
//   useEffect(() => {
//     const clickHandler = ({ target }: any) => {
//       if (!sidebar.current || !trigger.current) return;
//       if (
//         !sidebarOpen ||
//         sidebar?.current?.contains(target) ||
//         trigger?.current?.contains(target)
//       )
//         return;
//       setSidebarOpen(false);
//     };
//     document.addEventListener("click", clickHandler);
//     return () => document.removeEventListener("click", clickHandler);
//   });

//   // close if the esc key is pressed
//   useEffect(() => {
//     const keyHandler = ({ keyCode }) => {
//       if (!sidebarOpen || keyCode !== 27) return;
//       setSidebarOpen(false);
//     };
//     document.addEventListener("keydown", keyHandler);
//     return () => document.removeEventListener("keydown", keyHandler);
//   });

//   useEffect(() => {
//     localStorage.setItem("sidebar-expanded", sidebarExpanded);
//     if (sidebarExpanded) {
//       document.querySelector("body").classList.add("sidebar-expanded");
//     } else {
//       document.querySelector("body").classList.remove("sidebar-expanded");
//     }
//   }, [sidebarExpanded]);

//   const [menuItems, setMenuItems] = useState([
//     /*  { name: 'Home', link: '/management', icon: homeIcon, selected: false }, */
//     {
//       name: "Music and Video",
//       link: "/report",
//       icon: generalIcon,
//       selected: false,
//     },
//     { name: "Channels", link: "/channels", icon: generalIcon, selected: false },
//     { name: "Artist", link: "/artist", icon: generalIcon, selected: false },
//     { name: "Admin Tools", link: "/links", icon: generalIcon, selected: false },
//     { name: "Services", link: "/services", icon: generalIcon, selected: false },
//     { name: "Events", link: "/events", icon: generalIcon, selected: false },
//     { name: "News", link: "/coming-soon", icon: generalIcon, selected: false },
//     { name: "Blog", link: "/coming-soon", icon: generalIcon, selected: false },
//     {
//       name: "Oportunities",
//       link: "/coming-soon",
//       icon: generalIcon,
//       selected: false,
//     },
//     {
//       name: "Classes",
//       link: "/coming-soon",
//       icon: generalIcon,
//       selected: false,
//     },
//     { name: "Projects", link: "/projects", icon: generalIcon, selected: false },
//     {
//       name: "Store",
//       link: "/coming-soon",
//       icon: generalIcon,
//       selected: false,
//     },
//     {
//       name: "Booking",
//       link: "/coming-soon",
//       icon: generalIcon,
//       selected: false,
//     },
//     {
//       name: "Digital PressKit",
//       link: "/coming-soon",
//       icon: generalIcon,
//       selected: false,
//     },
//   ]);

//   return (
//     <Box as="aside" w="15%">
//       <ul className="space-y-4 px-2">
//         {/* Music & Video */}
//         <SidebarLinkGroup
//           activecondition={
//             pathname.includes("report") ||
//             pathname.includes("upload") ||
//             pathname.includes("content")
//           }
//         >
//           {(handleClick, open) => (
//             <>
//               <a
//                 href="#0"
//                 className="block text-white truncate transition duration-150 px-4 py-4 mb-4 last:mb-0 bg-alpha-blue rounded-2xl"
//                 onClick={(e) => {
//                   console.log("click");
//                   e.preventDefault();
//                   handleClick();
//                 }}
//               >
//                 <div className="flex items-center justify-between">
//                   <div className="flex items-center">
//                     <MusicNoteIcon className="shrink-0 h-6 w-6" />
//                     <span
//                       className={cn(
//                         "text-base ml-3 duration-200 font-medium",
//                         pathname.includes("report") ||
//                           pathname.includes("upload") ||
//                           pathname.includes("content")
//                           ? "font-bold hover:font-semibold hover:text-opacity-90"
//                           : "font-medium hover:font-bold",
//                       )}
//                     >
//                       Music and Video
//                     </span>
//                   </div>
//                   {/* Icon */}
//                   <div className="flex shrink-0 ml-2">
//                     <svg
//                       className={`w-3 h-3 shrink-0 ml-1 fill-current text-slate-400 ${
//                         open && "rotate-180"
//                       }`}
//                       viewBox="0 0 12 12"
//                     >
//                       <path d="M5.9 11.4L.5 6l1.4-1.4 4 4 4-4L11.3 6z" />
//                     </svg>
//                   </div>
//                 </div>
//               </a>
//               <div className={open ? "block" : "hidden"}>
//                 <ul className="pl-8 mt-1 space-y-2.5">
//                   <li>
//                     <NavLink
//                       end
//                       to="/upload"
//                       className={({ isActive }) =>
//                         cn(
//                           "block transition duration-150 truncate px-4",
//                           isActive
//                             ? "font-bold hover:font-semibold hover:text-opacity-90  border-l-2 border-white"
//                             : "hover:font-bold",
//                         )
//                       }
//                     >
//                       <span className="text-[15px] leading-[18px] duration-200">
//                         Upload
//                       </span>
//                     </NavLink>
//                   </li>
//                   <li>
//                     <NavLink
//                       end
//                       to="/report"
//                       className={({ isActive }) =>
//                         cn(
//                           "block transition duration-150 truncate px-4",
//                           isActive
//                             ? "font-bold hover:font-semibold hover:text-opacity-90  border-l-2 border-white"
//                             : "hover:font-bold",
//                         )
//                       }
//                     >
//                       <span className="text-[15px] leading-[18px] duration-200">
//                         Reports
//                       </span>
//                     </NavLink>
//                   </li>
//                   <li>
//                     <NavLink
//                       end
//                       to="/content"
//                       className={({ isActive }) =>
//                         cn(
//                           "block transition duration-150 truncate px-4",
//                           isActive
//                             ? "font-bold hover:font-semibold hover:text-opacity-90  border-l-2 border-white"
//                             : "hover:font-bold",
//                         )
//                       }
//                     >
//                       <span className="text-[15px] leading-[18px] duration-200">
//                         Content manager
//                       </span>
//                     </NavLink>
//                   </li>
//                 </ul>
//               </div>
//             </>
//           )}
//         </SidebarLinkGroup>
//         {/* Artist */}
//         <li
//           className={cn("px-4 py-4 rounded-2xl mb-2 last:mb-0 bg-alpha-blue")}
//           style={{ padding: "1rem" }}
//         >
//           <NavLink
//             end
//             to="/channels"
//             className={cn(
//               "block text-white truncate transition duration-150 font-medium",
//               pathname.includes("channels")
//                 ? "font-bold hover:font-semibold hover:text-opacity-90"
//                 : "hover:font-bold",
//             )}
//           >
//             <div className="flex items-center">
//               <MusicNoteIcon className="text-white" />
//               <span className="text-base ml-3 duration-200">Channels</span>
//             </div>
//           </NavLink>
//         </li>
//         <li
//           className={cn("px-4 py-4 rounded-2xl mb-2 last:mb-0 bg-alpha-blue")}
//           style={{ padding: "1rem" }}
//         >
//           <NavLink
//             end
//             to="/artist"
//             className={cn(
//               "block text-white truncate transition duration-150 font-medium",
//               pathname.includes("artist")
//                 ? "font-bold hover:font-semibold hover:text-opacity-90"
//                 : "hover:font-bold",
//             )}
//           >
//             <div className="flex items-center">
//               <MusicNoteIcon className="text-white" />
//               <span className="text-base ml-3 duration-200">Artist</span>
//             </div>
//           </NavLink>
//         </li>
//         <li
//           className={cn("px-4 py-4 rounded-2xl mb-2 last:mb-0 bg-alpha-blue")}
//           style={{ padding: "1rem" }}
//         >
//           <NavLink
//             end
//             to="/events"
//             className={cn(
//               "block text-white truncate transition duration-150 font-medium",
//               pathname.includes("events")
//                 ? "font-bold hover:font-semibold hover:text-opacity-90"
//                 : "hover:font-bold",
//             )}
//           >
//             <div className="flex items-center">
//               <MusicNoteIcon className="text-white" />
//               <span className="text-base ml-3 duration-200">Events</span>
//             </div>
//           </NavLink>
//         </li>
//         <li
//           className={cn("px-4 py-4 rounded-2xl mb-2 last:mb-0 bg-alpha-blue")}
//           style={{ padding: "1rem" }}
//         >
//           <NavLink
//             end
//             to="/presskit/:artistId"
//             className={cn(
//               "block text-white truncate transition duration-150 font-medium",
//               pathname.includes("presskit")
//                 ? "font-bold hover:font-semibold hover:text-opacity-90"
//                 : "hover:font-bold",
//             )}
//           >
//             <div className="flex items-center">
//               <MusicNoteIcon className="text-white" />
//               <span className="text-base ml-3 duration-200">PressKit</span>
//             </div>
//           </NavLink>
//         </li>
//       </ul>

//       {/* {menuItems.map((item, key) =>
//         item.name === 'Products' ? (
//           <Accordion
//             key={key}
//             defaultIndex={[0]}
//             allowMultiple
//             p="0"
//             m="0"
//             border="none"
//             _focus={{ border: 'none' }}
//           >
//             <AccordionItem border="none" m="0" p="0">
//               <AccordionButton
//                 border="none"
//                 m="0"
//                 p="0"
//                 _active={{}}
//                 _focus={{}}
//               >
//                 <SideBarItem
//                   data={item}
//                   handleOnChangeMenu={handleChangeItem}
//                   handleChange={handleChange}
//                 />
//               </AccordionButton>
//               <AccordionPanel w="100%">
//                 {LinkItems.map(link => {
//                   return (
//                     <NavItem
//                       link={link.link}
//                       key={link.link}
//                       icon={link.icon}
//                       handleChange={handleChange}
//                       name={link.name}
//                       selected={link.selected}
//                     >
//                       {link.name}
//                     </NavItem>
//                   );
//                 })}
//               </AccordionPanel>
//             </AccordionItem>
//           </Accordion>
//         ) : item?.name !== 'Admin Tools' ? (
//           <SideBarItem
//             key={key}
//             data={item}
//             handleOnChangeMenu={handleChangeItem}
//           />
//         ) : item?.name === 'Admin Tools' &&
//           (user?.id === '3a529586-1e6f-47f2-9a4e-80e54e2c4068' ||
//             user?.id === '55dbfce4-e8c0-47f3-878b-78003b544c86') ? (
//           <SideBarItem
//             key={key}
//             data={item}
//             handleOnChangeMenu={handleChangeItem}
//           />
//         ) : (
//           <></>
//         )
//       )} */}
//     </Box>
//   );
// }
