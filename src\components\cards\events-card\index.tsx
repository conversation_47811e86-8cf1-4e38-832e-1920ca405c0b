import {
  Avatar,
  Box,
  Button,
  ButtonGroup,
  Flex,
  Icon,
  Image,
  Popover,
  PopoverArrow,
  PopoverBody,
  PopoverCloseButton,
  PopoverContent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON>overHeader,
  PopoverTrigger,
  Text,
  useDisclosure,
  useToast,
} from "@chakra-ui/react";
import { Markup } from "interweave";
import React, { useEffect, useRef, useState } from "react";
import { useNavigate } from "react-router-dom";
import ModalViewService from "../../modal/modal-view-service";

import shareIconBlue from "../../../images/shareIconblue.svg";

import { BsTrash } from "react-icons/bs";
import { api_muskaLive } from "../../../services/api_muskaLive";
import { api_tickets } from "../../../services/api_tickets";

const EventsCard = (props: any) => {
  const { isOpen, onToggle, onClose } = useDisclosure();

  const toast = useToast();
  const [category, setName] = useState(props?.data?.category);
  const [description, setDesc] = useState(props?.data?.description);
  const [title, setTitle] = useState(props?.data?.name);
  const [batches, setBatches] = useState(props?.data?.batch);
  const [likes, setLikes] = useState("0");
  const [shares, setShares] = useState("0");
  const [comments, setComments] = useState("0");
  const [smallerTicketPrice, setSmallerTicketPrice] = useState(0);
  const [higherTicketPrice, setHigherTicketPrice] = useState(0);

  const linkRef = useRef<HTMLAnchorElement>(null);

  const [isOpened, setIsOpened] = useState(false);

  const toggleIsOpened = () => {
    //setIsOpened(true)
    if (props?.data?._id === "63a48f507275672606b61d60") {
      console.log("inside");
      if (linkRef && linkRef.current) linkRef.current.click();
    }
  };

  // useEffect(() => {
  //   getSmallerValue();
  // }, [props?.data]);

  // const getSmallerValue = () => {
  //   var smallerValue = props?.data?.batch[0]?.tickets[0]?.price;
  //   var higherValue = props?.data?.batch[0]?.tickets[0]?.price;

  //   props?.data?.batch?.map((batch: any) => {
  //     batch?.tickets?.map((ticket: any) => {
  //       if (ticket && ticket?.price < smallerValue)
  //         smallerValue = ticket?.price;
  //       if (ticket && ticket?.price > higherValue) higherValue = ticket?.price;
  //     });
  //   });

  //   setSmallerTicketPrice(smallerValue);
  //   setHigherTicketPrice(higherValue);
  // };

  const handleDeleteEvent = async () => {
    const deleteResponse: any = await api_tickets.delete(
      `/delete/${props?.data?._id}`,
    );

    if (deleteResponse?.status === 200) {
      toast({
        title: "Event deleted",
        description: "This event has been deleted.",
        status: "success",
        duration: 4000,
        isClosable: true,
      });
    }
  };

  return (
    <Box
      bgGradient="linear-gradient(245.34deg, rgba(46, 162, 240, .1) 11.95%, rgba(45, 206, 239, .1) 84.19%)"
      color="white"
      borderRadius="20px"
      pb="3"
      pt="0"
      cursor="pointer"
      onClick={toggleIsOpened}
    >
      <Flex justifyContent="space-between" flexDir="column" gap="13px">
        <Box w="100%" h="50%" position="relative">
          {/* <Image w="100%" h="200px" position="relative" src={props?.data?.cover} alt="images" /> */}
          <Box
            bgImage={props?.data?.cover}
            backgroundPosition="center center"
            backgroundRepeat="no-repeat"
            backgroundSize="cover"
            w="100%"
            h="200px"
            position="relative"
            borderRadius="20px"
          ></Box>

          <Flex
            flexDir="row"
            ml="2"
            mt="1"
            position="absolute"
            top="0"
            bgColor="rgba(00, 00, 00, .5)"
            borderRadius="10px"
            p="2"
            w="85%"
          >
            <Box>
              <Avatar h="30px" w="30px" src={props?.data?.channel?.images} />
            </Box>
            <Flex flexDir="column" ml="3">
              <Text fontWeight="bold" fontSize="9pt">
                {props?.data?.channel?.name}
              </Text>
            </Flex>
          </Flex>
        </Box>
        <a
          href={`https://event.muska.live/events/${props?.data?._id}`}
          target="_blank"
          ref={linkRef}
          hidden
        ></a>

        {/* <Text ml="5" mr="5" fontSize="9pt" color="#444"><Markup content={description} /></Text> */}

        <Box
          ml="3"
          bgColor="gray"
          border="1px solid gray"
          borderRadius="10px"
          w="20%"
          textAlign="center"
        >
          <Text fontSize="8pt" color="white">
            {props?.data?.category}
          </Text>
        </Box>

        <Box h="50px" ml="3">
          <Text mr="5" mb="1" fontSize="12pt" color="white" fontWeight="bold">
            {title}
          </Text>
        </Box>

        <Flex
          flexDir="row"
          justifyContent="space-between"
          alignItems="center"
          ml="3"
          mr="5"
        >
          <Text
            bgGradient="linear(to-l, rgba(46, 162, 240, 1), rgba(45, 206, 239, 1))"
            bgClip="text"
            fontSize="8pt"
          >
            {props?.data?.startDate?.slice(0, 10)}
          </Text>
        </Flex>

        <Flex ml="3" mb="3">
          <Text
            color="white"
            fontSize="14"
          >{`From ${smallerTicketPrice} CVE`}</Text>
        </Flex>

        <Flex gap="1" mb="3">
          <Button
            color="white"
            bgGradient="linear(to-r, #2EA2F0, #2DCEEF)"
            borderRadius="20"
            w="75%"
            ml="3"
            fontSize="10pt"
          >
            Buy Tickets
          </Button>

          <Button bgColor="transparent" _hover={{}} _focus={{}} _active={{}}>
            <Image src={shareIconBlue} />
          </Button>

          <Popover
            returnFocusOnClose={false}
            isOpen={isOpen}
            onClose={onClose}
            placement="right"
            closeOnBlur={false}
          >
            <PopoverTrigger>
              <Button
                bgColor="transparent"
                borderRadius="20"
                _hover={{}}
                _focus={{}}
                _active={{}}
                onClick={onToggle}
              >
                <BsTrash color="white" fontWeight="bold" fontSize="20pt" />
              </Button>
            </PopoverTrigger>
            <PopoverContent bgColor="myblue.100" _focus={{}} _active={{}}>
              <PopoverHeader fontWeight="semibold">Delete Event</PopoverHeader>
              <PopoverArrow />
              <PopoverCloseButton />
              <PopoverBody>
                Are you sure you want to delete this event?
              </PopoverBody>
              <PopoverFooter display="flex" justifyContent="flex-end">
                <ButtonGroup size="sm">
                  <Button variant="outline" onClick={onClose}>
                    Cancel
                  </Button>
                  <Button colorScheme="green" onClick={handleDeleteEvent}>
                    Delete event
                  </Button>
                </ButtonGroup>
              </PopoverFooter>
            </PopoverContent>
          </Popover>
        </Flex>
      </Flex>

      {/*  <ModalViewService setIsOpen={setIsOpened} isOpenService={isOpened} data={props?.data} /> */}
    </Box>
  );
};

export { EventsCard };
