import {
  ChannelsPaginatorInfo,
  ChannelsQueryOptions,
} from "../../types/channel";
import { MuskaLiveHttpClient } from "../../services/api_muskaLive";
import { API_ENDPOINTS } from "./api-endpoints";

export const channelClient = {
  me: ({ ...query }: Partial<ChannelsQueryOptions>) => {
    return MuskaLiveHttpClient.get<ChannelsPaginatorInfo>(
      API_ENDPOINTS.CHANNELS.ME,
      { ...query },
    );
  },
};
