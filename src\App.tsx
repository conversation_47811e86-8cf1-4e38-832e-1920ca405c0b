import React from "react";
import { ChakraProvider } from "@chakra-ui/react";
import "./styles/special.css";
import "./styles/styles.css";

import { BrowserRouter as Router, Route, Routes } from "react-router-dom";
import { AuthProvider } from "./contexts/auth-context";
import { theme } from "./styles/theme";
import Login from "./pages/Login";
import Dashboard from "./pages/Dashboard";
import Report from "./pages/Report";

import Upload from "./pages/Upload";
import Edit from "./pages/Edit";
import Fonts from "@/components/fonts/fonts";
import Album from "./pages/Albums";
import AlbumDetails from "./pages/AlbumDetails";
import ArtistDetails from "./pages/ArtistDetails";
import Artist from "./pages/Artist";
import Register from "./pages/register";
import Profile from "./pages/ProfilePage";
import { UploadProvider } from "./contexts/upload-context";
import Management from "./pages/Management";
import ValidarEmail from "./pages/validarEmail";
import Content from "./pages/ContentManager";
import Channels from "./pages/Channels";
import ChannelDetails from "./pages/ChannelDetails";
import InDev from "./pages/inDev";
import NotFound from "./pages/notfound";
import MoreDetails from "./pages/MoreDetails";
import Export from "./components/utils/export-report";
import ComingSoon from "./pages/betaServices";
import { Services } from "./pages/services/services";
import { CreateService } from "./pages/services/services/subcomponents/CreateService";
import ResetPassword from "./pages/ResetPassword/ResetPassword";
import VerifyBeforeReset from "./pages/ResetPassword/verifyEmail";
import { Events } from "./pages/events";
import { CreateTickets } from "./pages/events/subcomponents/CreateTickets";
import { Tickets } from "./pages/tickets";
import Projects from "./pages/Projects";

import LinkLabel from "./pages/links/label";
import UpdateAccount from "./pages/links/updateAccount";

import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import UpdateUploadPage from "./pages/UpdateUpload";

import { Toaster } from "sonner";
import Presskit from "./pages/presskit";
import AlbumsInfiniteList from "./pages/admin/contents";
import AlbumsInfiniteList2 from "./pages/admin/contents2";
import ProtectedRoutes, { withProtectedRoute } from "./utils/protected-routes";
import ArtistEdit from "./modules/artist/artist-edit-page";
import TopTracks from "./modules/admin/top-tracks/top-tracks-page";

//

const queryClient = new QueryClient();

export const clearQueryCache = () => {
  queryClient.clear(); // Clears the entire cache
};

function App() {
  const ProtectedReport = withProtectedRoute(Report);

  return (
    <QueryClientProvider client={queryClient}>
      <ChakraProvider resetCSS theme={theme} cssVarsRoot={undefined}>
        <Fonts />
        <Toaster position="top-right" richColors closeButton />
        <Router>
          <AuthProvider>
            <Routes>
              <Route path="/" element={<Login />} />
              <Route path="/login/:redirect" element={<Login />} />
              <Route path="/register" element={<Register />} />
              <Route path="/resetPassword" element={<ResetPassword />} />
              <Route
                path="/emailValidateToReset"
                element={<VerifyBeforeReset />}
              />
              <Route path="/emailValidate" element={<ValidarEmail />} />

              <Route path="/report" element={<ProtectedReport />} />

              <Route path="/management" element={<Management />} />
              <Route path="/content" element={<Content />} />
              <Route path="/channels" element={<Channels />} />
              <Route path="/channels/:id" element={<ChannelDetails />} />
              <Route path="/coming-soon" element={<ComingSoon />} />
              <Route path="/export" element={<Export />} />
              <Route path="/in-dev" element={<InDev />} />

              <Route path="/projects" element={<Projects />} />

              <Route path="/404" element={<NotFound />} />
              <Route
                path="/dashboard"
                element={
                  <ProtectedRoutes>
                    <Dashboard />
                  </ProtectedRoutes>
                }
              />

              <Route
                path="/upload"
                element={
                  <ProtectedRoutes>
                    <UploadProvider>
                      <Upload />
                    </UploadProvider>
                  </ProtectedRoutes>
                }
              />

              <Route path="/edit/:id" element={<Edit />} />

              <Route
                path="/album"
                element={
                  <ProtectedRoutes>
                    <Album />
                  </ProtectedRoutes>
                }
              />
              <Route
                path="/profile"
                element={
                  <ProtectedRoutes>
                    <Profile />
                  </ProtectedRoutes>
                }
              />
              <Route
                path="/upload/:id/:step"
                element={
                  <ProtectedRoutes>
                    <UploadProvider>
                      <UpdateUploadPage />
                    </UploadProvider>
                  </ProtectedRoutes>
                }
              />
              <Route
                path="/album/:id"
                element={
                  <ProtectedRoutes>
                    <UploadProvider>
                      <AlbumDetails
                        fromAlb={false}
                        handleTabsChange={() => {}}
                      />
                    </UploadProvider>
                  </ProtectedRoutes>
                }
              />
              <Route
                path="/moredetails/:id"
                element={
                  <ProtectedRoutes>
                    <UploadProvider>
                      <MoreDetails albumId={null} handleTabsChange={() => {}} />
                    </UploadProvider>
                  </ProtectedRoutes>
                }
              />
              <Route
                path="/artist"
                element={
                  <ProtectedRoutes>
                    <Artist info="" />
                  </ProtectedRoutes>
                }
              />
              <Route
                path="/artist/:id"
                element={
                  <ProtectedRoutes>
                    <ArtistDetails />
                  </ProtectedRoutes>
                }
              />
              <Route
                path="/artist/:id/edit"
                element={
                  <ProtectedRoutes>
                    <ArtistEdit />
                  </ProtectedRoutes>
                }
              />
              <Route
                path="/services"
                element={
                  <ProtectedRoutes>
                    <Services />
                  </ProtectedRoutes>
                }
              />
              <Route path="/create-service" element={<CreateService />} />
              <Route
                path="/tickets"
                element={
                  <ProtectedRoutes>
                    <Tickets />
                  </ProtectedRoutes>
                }
              />
              {/* <Route
                path="/events"
                element={
                  <ProtectedRoutes>
                    <Events />
                  </ProtectedRoutes>
                }
              /> */}
              <Route path="/create-tickets" element={<CreateTickets />} />
              <Route path="/linkLabel/:id" element={<UpdateAccount />} />
              <Route path="/links" element={<LinkLabel />} />
              <Route path="/events" element={<Events />} />
              <Route path="/presskit" element={<Presskit />} />
              <Route path="/admin/contents" element={<AlbumsInfiniteList />} />
              <Route
                path="/admin/contents2"
                element={<AlbumsInfiniteList2 />}
              />
              <Route
                path="/admin/top-tracks"
                element={
                  <ProtectedRoutes>
                    <TopTracks />
                  </ProtectedRoutes>
                }
              />
              {/* <Route exact path="/services" element={<CommingSoon />} /> */}
            </Routes>
          </AuthProvider>
        </Router>
      </ChakraProvider>
    </QueryClientProvider>
  );
}

export default App;
