import { MuskaHttpClient } from "@/services/api";
import { Channel } from "./types";

export const CHANNEL_ENDPOINTS = {
  CREATE: "/label/create",
  GET_USER_CHANNELS: "/label/userLabel/:userId?name=:search",
};

export const channelClient = {
  create: (data: Channel) => {
    return MuskaHttpClient.post(CHANNEL_ENDPOINTS.CREATE, data);
  },
  getUserChannels: (userId: string, search: string) => {
    return MuskaHttpClient.get(
      CHANNEL_ENDPOINTS.GET_USER_CHANNELS.replace(":userId", userId),
      { ":search": search },
    );
  },
};
