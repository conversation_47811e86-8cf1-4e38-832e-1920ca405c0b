import { useState } from "react";
import Header from "@/components/header/header";
import Sidebar from "@/components/sidebar/sidebar";

import { useNavigate } from "react-router-dom";

import "react-datepicker/dist/react-datepicker.css";

import leftIcon from "../images/lefticon.svg";

import Alert from "../components/utils/alert";

import ChannelsCard from "../components/cards/channels-card";
import ChannelsCardNew from "../components/cards/channels-card-new";

import CreateChannel from "../components/modal/create-channel";
import { useChannelsQuery } from "../data/channel";
import React from "react";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { BackIcon } from "@/components/icons/back-arrow-icon";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import BaseLayout from "@/components/layout/BaseLayout";
import { Loader2 } from "lucide-react";

export default function Channels() {
  const navigate = useNavigate();

  const [alert1, setAlert1] = useState(true);

  const { channels, loading, error } = useChannelsQuery({});
  

  // if (loading) {
  //   return <div>Loading...</div>;
  // }

  // if (error) {
  //   return <div>Error...</div>;
  // }

  return (
    <BaseLayout>
      <div className="rounded-xl flex-1  bg-[#151E33] p-4">
        <div className="flex justify-between items-center">
          <Tabs defaultValue="channels">
            <div className="flex justify-between p-1 w-full bg-[#19233A] rounded-[70px]">
              <div>
                <Button
                  className="bg-[#19233A] w-10 mr-10 h-10 p-2 rounded-full"
                  onClick={() => navigate(-1)}
                >
                  <BackIcon />
                </Button>
                <TabsList defaultValue="channels" className="bg-transparent">
                  <TabsTrigger
                    value="channels"
                    className="mr-8 border-b-4 border-transparent bg-transparent font-bold text-white focus:border-b-2 focus:border-myblue-200 data-[state=active]:border-myblue-300"
                  >
                    Channels
                  </TabsTrigger>
                </TabsList>
              </div>
              <></>
            </div>

            <TabsContent value="channels">
              <div className=" flex flex-col gap-10 px-10 py-10">
                <Label className=" text-white text-4xl">Channels</Label>
                <div className="w-full py-1 flex flex-col gap-8">
                  <Alert
                    text="To upload a video you need to create a channel, you can create a channel and then associate the channel content with an artist who has it or any other artist in the MUSKA universe."
                    show={alert1}
                    onClose={() => setAlert1(false)}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4 pt-8 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6">
                  {loading ? (
                    <div className="w-full  flex items-center justify-center">
                      <Loader2 className="w-10 h-10 animate-spin" />
                    </div>
                  ) : (
                    channels?.map((channel, index) => (
                      <ChannelsCard
                        data={channel}
                        key={index}
                        onClick={() => navigate("/channels/" + channel.id)}
                      />
                    ))
                  )}
                  {/* <button
                          title="Create Channel"
                          onClick={() => setIsCreateChannelOpen(true)}
                        >
                          <ChannelsCardNew />
                        </button> */}
                  <div className="flex flex-col items-center gap-5 font-bold">
                    <CreateChannel />

                    <p>New Channel</p>
                  </div>
                  {/*  isCreateChaneelOpern={isCreateChannelOpen}
                         setisCreateChaneelOpern={setIsCreateChannelOpen} */}
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </BaseLayout>
  );
}
