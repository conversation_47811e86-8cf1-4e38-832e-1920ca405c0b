import React from "react";
import { Box, Flex } from "@chakra-ui/react";
import {
  PDFDownloadLink,
  Document,
  Page,
  Text,
  usePDF,
  View,
  Svg,
  StyleSheet,
  Polygon,
  G,
  Image,
  Font,
} from "@react-pdf/renderer";

import logo from "../../images/logo-black.png";

Font.register({
  family: "Lato",
  src: "http://fonts.googleapis.com/css2?family=Lato:400",
  fontStyle: "normal",
  fontWeight: "normal",
});

const styles = StyleSheet.create({
  page: { backgroundColor: "white", color: "black", padding: "20px" },
  header: {
    color: "white",
    textAlign: "center",
    alignItems: "center",
    marginTop: "10px",
    justifyContent: "space-between",
    display: "flex",
    flexDirection: "row",
    width: "100%",
    fontSize: "12px",
  },
  listItem: {
    fontWeight: "bold",
    color: "black",
    textAlign: "center",
    marginTop: 0,
    marginBottom: 4,
    justifyContent: "space-between",
    width: "100%",
    flexDirection: "row",
    paddingHorizontal: 5,
    paddingVertical: 15,
    alignItems: "center",
  },
  grid: {
    color: "white",
    textAlign: "center",
    margin: 10,
    justifyContent: "space-between",
    width: "100%",
  },
  logo: { width: "100%", height: "100%" },
  titleContainer: {
    justifyContent: "space-between",
    width: "100%",
    flexDirection: "row",
  },
  title: {
    fontFamily: "Helvetica",
    fontWeight: "bold",
    marginBottom: "10px",
    width: "25%",
    fontSize: "12px",
  },
  titleOne: {
    fontFamily: "Helvetica",
    fontWeight: "bold",
    marginBottom: "10px",
    width: "25%",
    fontSize: "12px",
    textAlign: "left",
  },
  listHeader: {
    marginTop: 70,
    fontWeight: "bold",
    color: "black",
    textAlign: "center",
    justifyContent: "space-between",
    width: "100%",
    flexDirection: "row",
    alignItems: "center",
    borderBottom: "2px",
  },
  card: {
    width: "20%",
    flexDirection: "row",
    backgroundColor: "rgba(255, 255, 255, 1)",
  },

  cartTitle: {
    color: "rgba(73, 71, 71, 1)",
    fontSize: "10px",
    fontFamily: "Helvetica",
    fontWeight: "normal",
  },

  cardValue: {
    color: "rgba(73, 71, 71, 1)",
    fontSize: "10px",
    fontFamily: "Helvetica",
    fontWeight: "bold",
  },
});

const MyDoc = (props: any) => (
  <Document>
    <Page size="A4" style={styles.page} orientation="portrait" wrap>
      <View style={styles.header}>
        <View
          style={{
            width: "192px",
            height: "40px",
            position: "relative",
          }}
        >
          <Image src={logo} style={styles.logo} />
        </View>
        <View
          style={{
            color: "black",
            alignItems: "flex-end",
            textAlign: "right",
            flexDirection: "row",
          }}
        >
          <Text
            render={() =>
              "Date exported: " + props?.d + " / " + props?.m + " / " + props?.y
            }
          />
        </View>
      </View>
      <View
        style={{
          fontSize: "12px",
          display: "flex",
          justifyContent: "space-between",
          flexDirection: "row",
          marginTop: 40,
        }}
      >
        <View style={{ flexDirection: "column" }}>
          <Text
            render={() => "Total plays: " + props?.data?.totalPlay}
            style={{ marginBottom: 13 }}
          />

          <Text render={() => "Revenue: $ " + props?.data?.totalPlay * 0.001} />
        </View>
        <View>
          <Text render={() => "Report nº: ####"} style={{ marginBottom: 13 }} />

          <Text render={() => props?.data?.m + " " + props?.data?.y} />
        </View>
      </View>
      <View>
        <View
          style={{
            marginTop: 30,
            paddingTop: 10,
            width: "100%",
            flexDirection: "row",
            alignItems: "flex-start",
            borderTop: "2px",
          }}
        >
          <View style={styles.card}>
            <Text style={styles.cartTitle}>{"Total Plays: "} </Text>
            <Text
              style={styles.cardValue}
              render={() => props?.data?.totalPlay}
            />
          </View>
          <View style={styles.card}>
            <Text style={styles.cartTitle}>{"Rest of the World: "}</Text>
            <Text
              style={styles.cardValue}
              render={() =>
                props?.data?.list && props?.data?.list["Rest of the World"]
                  ? props?.data?.list["Rest of the World"]
                  : "0"
              }
            />
          </View>
          <View style={styles.card}>
            <Text style={styles.cartTitle}>{"Congo: "}</Text>
            <Text
              style={styles.cardValue}
              render={() =>
                props?.data?.list && props?.data?.list["Congo"]
                  ? props?.data?.list["Congo"]
                  : "0"
              }
            />
          </View>
          <View style={styles.card}>
            <Text style={styles.cartTitle}>{"Cape Verde: "}</Text>
            <Text
              style={styles.cardValue}
              render={() =>
                props?.data?.list && props?.data?.list["Cape Verd"]
                  ? props?.data?.list["Cape Verd"]
                  : "0"
              }
            />
          </View>
        </View>
      </View>

      <View style={styles.listHeader}>
        <Text style={styles.titleOne}>Album</Text>
        <Text style={styles.title}>Artist</Text>
        <Text style={styles.title}>Plays</Text>
        <Text style={styles.title}>Revenue</Text>
      </View>

      {props?.data?.listFinancial &&
        props?.data?.listFinancial?.map((item: any) => (
          <View style={styles.listItem} wrap>
            {/*TODO*/}
            {/*<Image src={{ uri: `${"https://muska.s3-eu-west-1.amazonaws.com/" + item?.image}`, method: 'GET', headers: { "Access-Control-Allow-Origin": "*" }, }} style={styles.logo} />*/}
            <Text
              render={() => item?.name}
              wrap
              style={{ width: "25%", textAlign: "left", fontSize: "10px" }}
            />
            <Text
              render={() => item?.artist?.name}
              style={{ width: "25%", fontSize: "10px" }}
            />
            <Text
              render={() => item?.total}
              style={{ width: "25%", fontSize: "10px" }}
            />
            <Text
              render={() => item?.revenue}
              style={{ width: "25%", fontSize: "10px" }}
              wrap
            />
          </View>
        ))}
    </Page>
  </Document>
);

const EmptyDoc = () => {
  return (
    <Document>
      <Page size="A4" style={styles.page} orientation="landscape" wrap>
        <View>
          <Text>Empty</Text>
        </View>
      </Page>
    </Document>
  );
};

export default function ExportUtil(props: any) {
  let newDate = new Date();
  //const [instance, updateInstance] = usePDF({ document: MyDoc });

  //if (instance?.error) return <Box>Something went wrong: {instance.error}</Box>;

  return (
    <PDFDownloadLink
      document={
        props !== null && props !== undefined ? (
          <MyDoc
            data={props}
            m={
              newDate.getMonth() + 1 > 9
                ? newDate.getMonth() + 1
                : "0" + (newDate.getMonth() + 1)
            }
            y={newDate.getFullYear()}
            d={
              newDate.getDate() > 9
                ? newDate.getDate()
                : "0" + newDate.getDate()
            }
          />
        ) : (
          <EmptyDoc />
        )
      }
      fileName={`${props?.user?.name} - ${props?.m}/${props?.y}.pdf`}
      style={{
        backgroundColor: "#2DCEEF",
        borderRadius: "30px",
        alignItems: "center",
        textAlign: "center",
        padding: "10px",
        paddingLeft: "20px",
        paddingRight: "20px",
        width: "150px",
        fontWeight: "bold",
      }}
    >
      {({ blob, url, loading, error }) =>
        loading ? <span>...</span> : <span>Export</span>
      }
    </PDFDownloadLink>
  );
}
