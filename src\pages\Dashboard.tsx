import React, { useState, useContext, useEffect } from "react";
import Header from "../components/header/header";
import Sidebar from "../components/sidebar/sidebar";
import { Flex, Box, Text, Center, Spinner } from "@chakra-ui/react";
import { api } from "../services/api";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import Slider from "react-slick";
// import CardItems from "../components/Artist/card-items";
import { AuthContext } from "../contexts/auth-context";

export default function Report() {
  const { useId, userType, user } = useContext(AuthContext);
  const [artists, setListArtists] = useState([]);
  // const [labels, setLabels] = useState([]);

  const [isLoading, setIsLoading] = useState(false);
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      //const response = await api.get(`artist/listArtists/${useId}`)
      const response: any = await api.get(
        `artist/viewArtistMySql/${user.provider.id}`,
      );
      const data: any = await response.data;
      setListArtists(data);
      setIsLoading(false);
    };
    fetchData();
  }, [useId]);

  // const getListLabels = useCallback(() => {
  //   api.get(`label/listAll`).then((res => {
  //     setLabels(res.data)
  //   }))
  // }, [])

  const printCardArtist = () => {
    // if (artists.length === 0) {
    //   return (<Text textAlign="center" fontSize='5xl'>Sem dados</Text>);
    // } else {
    return artists.map((item, index) => {
      return (
        <>testing</>
        // <CardItems
        //   data={item}
        //   key={index}
        //   routerTo="artistById"
        //   borderRadius="50%"
        // />
      );
    });
    // }
  };
  // const printCardChannel = () => {
  //   const items = []
  //   for (let index = 0; index < 10; index++) {
  //     items.push(<CardItems key={index} routerTo="channelById" borderRadius="60px" />);
  //   }
  //   return items;
  // }
  // const printCardLabel = () => {
  //   return labels.map((item, index) => {
  //     return (<CardItems routerTo="labelById" borderRadius="30px" data={item} key={index} />)
  //   })
  // }
  var settings = {
    dots: false,
    speed: 500,
    slidesToShow: 5,
    slidesToScroll: 5,
    initialSlide: 0,
    infinite: false,
    arrows: true,
    responsive: [
      {
        breakpoint: 1024,
        settings: {
          slidesToShow: 4,
          slidesToScroll: 1,
        },
      },
      {
        breakpoint: 600,
        settings: {
          slidesToShow: 4,
          slidesToScroll: 2,
          initialSlide: 2,
        },
      },
      {
        breakpoint: 480,
        settings: {
          slidesToShow: 3,
          slidesToScroll: 1,
        },
      },
    ],
  };

  return (
    <Flex direction="column" h="100vh">
      <Header />
      <Flex width="100%" my="6" mx="auto">
        <Sidebar />
        <Box
          className="muskaDataSection"
          width="80%"
          borderRadius="md"
          flex="1"
          px="6"
          bg="myblue.100"
        >
          {isLoading ? (
            <Flex justifyContent="center" alignItems="center">
              <Box>
                <Spinner
                  thickness="4px"
                  speed="0.65s"
                  emptyColor="gray.200"
                  color="blue.500"
                  size="xl"
                />
              </Box>
            </Flex>
          ) : (
            <>
              {userType === 4 && (
                <>
                  <Box marginBottom="32px">
                    {artists.length !== 0 ? (
                      <>
                        <Text
                          mb="30"
                          display="block"
                          textColor="white"
                          fontSize="4xl"
                          as="b"
                        >
                          {" "}
                          Artist{" "}
                        </Text>
                        <Slider {...settings}>{printCardArtist()}</Slider>
                      </>
                    ) : (
                      <Center fontSize="2xl" h="100px" color="white">
                        No data
                      </Center>
                    )}
                  </Box>
                </>
              )}
            </>
          )}
        </Box>
      </Flex>
    </Flex>
  );
}
{
  /* <Box>
            <Text mb="30" display="block" textColor="white" fontSize="4xl" as="b"> Canais </Text>
            <Slider {...settings}>
              {printCardChannel()}
            </Slider>
          </Box> */
}
{
  /* 
          <Box marginTop="32px">
            <Text mb="30" display="block" textColor="white" fontSize="4xl" as="b"> Label </Text>
            <Slider {...settings}>
              {printCardLabel()}
            </Slider>
          </Box> */
}
