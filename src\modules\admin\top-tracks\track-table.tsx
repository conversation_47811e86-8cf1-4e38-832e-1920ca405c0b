"use client";

import React, { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Play, Music, Album, Tag, Download } from "lucide-react";
import { Badge } from "@/components/ui/v2/badge";

interface Track {
  id: string;
  name: string;
  url: string;
  isrc: string;
  image: string;
  album: {
    id: string;
    name: string;
  };
  genre: {
    id: string;
    name: string;
  };
}

export default function TracksTable({ tracks = [] }: { tracks: Track[] }) {
  const [selectedTrack, setSelectedTrack] = useState<string | null>(null);

  const getGenreColor = (genre: string) => {
    const colors: { [key: string]: string } = {
      Rap: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",
      Gospel:
        "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300",
      <PERSON><PERSON><PERSON>:
        "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
      "Afro-pop":
        "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300",
      "Afro Pop":
        "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300",
      Zouk: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
      "Hip hop":
        "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300",
    };
    return (
      colors[genre] ||
      "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"
    );
  };

  const getDownloadFileName = (track: Track) => {
    const fileExtension = track.url.split(".").pop() || "mp3";
    return `${track.name.replace(/[^a-zA-Z0-9]/g, "_")}.${fileExtension}`;
  };

  return (
    <div className="w-full mx-auto py-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Music className="h-6 w-6" />
            Top Listen Tracks
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-12"></TableHead>
                  <TableHead>Track</TableHead>
                  <TableHead className="hidden md:table-cell">Album</TableHead>
                  <TableHead>Genre</TableHead>
                  <TableHead className="w-24">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {tracks.map((track, index) => (
                  <TableRow key={track.id} className="hover:bg-muted/50">
                    <TableCell>
                      <div className="flex items-center justify-center w-10 h-10 rounded-md bg-muted">
                        {track.image ? (
                          <img
                            src={
                              track.image
                                ? `https://muska.s3-eu-west-1.amazonaws.com/${track.image}`
                                : "/placeholder.svg"
                            }
                            alt={track.name}
                            width={40}
                            height={40}
                            className="rounded-md object-cover"
                          />
                        ) : (
                          <Album className="h-5 w-5 text-muted-foreground" />
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="font-medium leading-none">
                          {track.name}
                        </div>
                        <div className="text-sm text-muted-foreground md:hidden">
                          {track.album.name}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="hidden md:table-cell">
                      <div className="flex items-center gap-2">
                        <Album className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">{track.album.name}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant="secondary"
                        className={getGenreColor(track.genre.name)}
                      >
                        <Tag className="h-3 w-3 mr-1" />
                        {track.genre.name}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() =>
                          setSelectedTrack(
                            selectedTrack === track.id ? null : track.id,
                          )
                        }
                        className="h-8 w-8 p-0"
                      >
                        <Play className="h-4 w-4" />
                      </Button>
                      {/* add donload button */}
                      <a
                        href={`https://muska.s3-eu-west-1.amazonaws.com/${track.url}`}
                        download={getDownloadFileName(track)}
                        className="inline-flex items-center justify-center h-8 w-8 p-0 rounded-md hover:bg-accent hover:text-accent-foreground transition-colors"
                        title="Download track"
                      >
                        <Download className="h-4 w-4" />
                      </a>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {selectedTrack && (
            <div className="mt-4 p-4 bg-muted rounded-lg">
              <div className="text-sm text-muted-foreground">
                Selected track:{" "}
                {tracks.find((t) => t.id === selectedTrack)?.name}
              </div>
              <div className="text-xs text-muted-foreground mt-1">
                URL: {tracks.find((t) => t.id === selectedTrack)?.url}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
