{"name": "muska-upload", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "vite --port 3010", "build": "vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@chakra-ui/accordion": "^2.3.0", "@chakra-ui/alert": "^2.2.0", "@chakra-ui/image": "^2.1.0", "@chakra-ui/react": "^2.8.0", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@headlessui/react": "^1.7.18", "@hookform/resolvers": "^3.10.0", "@leecheuk/react-google-login": "^5.4.1", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-aspect-ratio": "^1.0.3", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.0.7", "@react-pdf/renderer": "^3.0.0", "@tanstack/react-query": "^4.35.3", "@testing-library/jest-dom": "^5.9.0", "@testing-library/react": "^10.2.1", "@testing-library/user-event": "^12.0.2", "@tinymce/tinymce-react": "^5.0.1", "@types/pdfmake": "^0.2.2", "@types/react": "^18.2.16", "@types/react-dom": "^18.2.7", "assert": "^2.0.0", "axios": "^1.7.1", "buffer": "^6.0.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "cropperjs": "^1.6.1", "cross-env": "^7.0.3", "date-fns": "^4.1.0", "firebase": "^9.23.0", "framer-motion": "^4.1.17", "fs": "^0.0.1-security", "googleapis": "^133.0.0", "graphql": "^16.10.0", "graphql-request": "^7.1.2", "html2canvas": "^1.4.1", "id3js": "^2.1.1", "immutability-helper": "^3.1.1", "interweave": "^13.0.0", "jsmediatags": "^3.9.7", "jspdf": "^2.5.1", "jwt-decode": "^4.0.0", "lucide-react": "^0.323.0", "milestone-progress-bar": "^1.0.6", "moment": "^2.29.4", "music-duration": "^1.0.2", "nookies": "^2.5.2", "pdfmake": "^0.2.5", "radix-ui": "^1.1.3", "react": "^18.3.1", "react-contenteditable": "^3.3.6", "react-cropper": "^2.3.3", "react-datepicker": "^4.3.0", "react-day-picker": "^8.10.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.3.1", "react-dropzone": "^14.2.3", "react-hook-form": "^7.54.2", "react-icons": "^3.0.0", "react-moment": "^1.1.2", "react-pdf": "^8.0.2", "react-phone-input-2": "^2.15.1", "react-player": "^2.11.0", "react-router-dom": "^6.3.0", "react-select": "^5.1.0", "react-select-async-paginate": "^0.6.0", "react-slick": "^0.28.1", "react-use-audio-player": "^2.2.0", "sass": "^1.52.1", "slick-carousel": "^1.8.1", "sonner": "^1.4.41", "strtok": "^0.1.1", "suneditor": "^2.46.3", "suneditor-react": "^3.6.1", "tabs": "^0.2.0", "tailwind-merge": "^1.14.0", "tailwindcss-animate": "^1.0.7", "tinymce": "^7.1.0", "tw-animate-css": "^1.2.4", "v0": "^1.1.1", "vite-plugin-require": "^1.1.14", "web-vitals": "^0.2.2", "yup": "^0.32.11", "zod": "^3.24.2"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@rollup/rollup-win32-x64-msvc": "^4.41.1", "@types/node": "^20.12.3", "@types/pdfmake": "^0.2.2", "@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@types/react-slick": "^0.23.10", "@types/websocket": "^1.0.5", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "@vitejs/plugin-react": "^4.2.1", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.21", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "exports-loader": "^4.0.0", "imports-loader": "^4.0.1", "postcss": "^8.5.3", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.11", "tailwindcss": "^3.4.17", "typescript": "^5.8.3", "vite": "^5.2.0"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "^4.14.0"}}