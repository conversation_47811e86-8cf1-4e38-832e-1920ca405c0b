import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { CHANNEL_ENDPOINTS, channelClient } from "./client";

export const useCreateChannel = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: channelClient.create,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [CHANNEL_ENDPOINTS.GET_USER_CHANNELS],
      });
    },
  });
};

export const useGetUserChannels = (userId: string, search: string) => {
  const { data, error, isLoading } = useQuery({
    queryKey: [CHANNEL_ENDPOINTS.GET_USER_CHANNELS, userId, search],
    queryFn: () => channelClient.getUserChannels(userId, search),
    enabled: Boolean(userId),
  });

  return {
    channels: data ?? [],
    error,
    isLoading,
  };
};
