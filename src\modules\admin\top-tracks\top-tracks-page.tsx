import BaseLayout from "@/components/layout/BaseLayout";
import React, { useEffect } from "react";
import { useTopTracksQuery } from "./hooks";
import DatePicker from "@/components/date-picker";
import { Input } from "@/components/ui/v2/input";
import { Label } from "@/components/ui/v2/label";
import TracksTable from "./track-table";
import { toast } from "sonner";

function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = React.useState<T>(value);

  React.useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => clearTimeout(timer);
  }, [value, delay]);

  return debouncedValue;
}

export default function TopTracks() {
  const [start, setStart] = React.useState("");
  const [end, setEnd] = React.useState("");
  const [limit, setLimit] = React.useState(10);

  const debouncedLimit = useDebounce(limit, 2000);

  const { data, isLoading, error } = useTopTracksQuery({
    start,
    end,
    limit: debouncedLimit,
  });

  const handleLimitChange = (e: any) => {
    const value = Number(e.target.value);
    if (!isNaN(value) && value >= 0) {
      setLimit(value);
    }
  };

  useEffect(() => {
    if (isLoading) {
      toast.loading("Loading top tracks...");
    } else {
      toast.dismiss();
    }
  }, [isLoading]);

  return (
    <BaseLayout>
      <div className="flex flex-col gap-5">
        <h1 className="text-3xl font-bold">Top Tracks</h1>
        <div className="flex gap-5">
          <div className="bg-white p-5 rounded-lg bg-opacity-5">
            <DatePicker label="Start Date" onChange={setStart} />
          </div>
          <div className="bg-white p-5 rounded-lg bg-opacity-5">
            <DatePicker label="End Date" onChange={setEnd} />
          </div>
          <div className="bg-white p-5 rounded-lg bg-opacity-5">
            <div className="flex flex-col items-start justify-center gap-2">
              <Label htmlFor="limit" className="text-lg">
                Limit
              </Label>
              <Input
                type="number"
                id="limit"
                value={limit}
                onChange={handleLimitChange}
                className="border border-gray-300 rounded-md p-2 h-10"
              />
            </div>
          </div>
        </div>
        <TracksTable tracks={data?.topListenTracks ?? []} />
      </div>
    </BaseLayout>
  );
}
