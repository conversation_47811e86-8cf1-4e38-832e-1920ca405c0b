"use client";
import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  PopoverTrigger,
  PopoverContent,
  Popover,
} from "@/components/ui/popover";
import {
  CommandInput,
  CommandEmpty,
  CommandItem,
  CommandGroup,
  Command,
  CommandList,
} from "@/components/ui/command";
import ModalCreateLabel from "../modal/modal-create-label";

export function ChannelSelector({ channels, setChannelId, channelId }: any) {
  /*  const [labelList, setLabelList] = useState<any[]>([]); */
  const [open, setOpen] = useState(false);

  const [isCreateLabelOpen, setIsCreateLabelOpen] = useState(false);

  const handleSelectChannel = (channel: any) => {
    setChannelId(channel);
    setOpen(false);
  };

  const toggleOpenCreateLabel = () => {
    setIsCreateLabelOpen(true);
  };

  return (
    <div className="flex w-full">
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger className="rounded-full bg-transparent w-full border p-3 flex justify-between z-10 items-center">
          <span
            className=" text-white cursor-pointer"
            onClick={() => setOpen(true)}
          >
            Select or create a channel
          </span>
          <ChevronsUpDownIcon className=" h-4 w-4 shrink-0 opacity-50" />
        </PopoverTrigger>
        <PopoverContent className="w-[500px] p-0 bg-myblue-100 text-white">
          <Command
            className="w-full bg-transparent text-white"
            // filter={(value, search) => {
            //   if (value.includes(search)) return 1
            //   return 0
            // }}
          >
            <CommandInput
              className="h-9 w-full text-white"
              placeholder="Search channels..."
            />
            <Button
              className="text-left bg-transparent hover:bg-slate-400"
              onClick={toggleOpenCreateLabel}
            >
              <span className="text-left w-full">Create</span>
            </Button>
            <CommandList className="w-full text-white bg-transparent">
              <CommandEmpty>No channel found.</CommandEmpty>
              <CommandGroup className="w-full ">
                {channels?.map((channel: any, index: number) => (
                  <CommandItem
                    className="w-full bg-transparent text-white"
                    value={channel}
                    key={index}
                    onSelect={() => handleSelectChannel(channel?.id)}
                  >
                    {channel?.name}
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>

      <ModalCreateLabel
        isOpen={isCreateLabelOpen}
        setIsOpen={setIsCreateLabelOpen}
      />
    </div>
  );
}

function ChevronsUpDownIcon(props: any) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="m7 15 5 5 5-5" />
      <path d="m7 9 5-5 5 5" />
    </svg>
  );
}
